# proxy conf

worker_rlimit_nofile        100000;

error_log                   "/home/<USER>/cai/logs/error_log" warn;
pid                         logs/tengine-proxy.pid;

events {
    use                     epoll;
    worker_connections      20480;
}

# include dso.conf;
#include tmd_main.conf;

http {
    include                 mime.types;
    default_type            application/octet-stream;

    root                    htdocs;

    sendfile                on;
    tcp_nopush              on;

    server_tokens           off;

    keepalive_timeout       0;

    client_header_timeout   1m;
    send_timeout            1m;
    client_max_body_size    100m;
    client_body_temp_path   data/client_body;

    index                   index.html index.htm;

    log_format              proxyformat    "$remote_addr $http_x_readtime [$time_local] \"$request_method http://$host$request_uri\" $status $body_bytes_sent \"$http_referer\" \"$upstream_addr\" \"$http_user_agent\" \"$cookie_unb\" \"$cookie_cookie2\"";

    access_log              "/home/<USER>/cai/logs/taobao-access_log" proxyformat;
    log_not_found           off;

    gzip                    off;
    gzip_http_version       1.0;
    gzip_comp_level         6;
    gzip_min_length         1024;
    gzip_proxied            any;
    gzip_vary               on;
    gzip_disable            msie6;
    gzip_buffers            96 8k;
    gzip_types              text/xml text/plain text/css application/javascript application/x-javascript application/rss+xml application/json;

    proxy_set_header        Host $host;
    proxy_set_header        X-Real-IP $remote_addr;
    proxy_set_header        Web-Server-Type nginx;
    proxy_set_header        WL-Proxy-Client-IP $remote_addr;
    proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_redirect          off;
    proxy_buffers           128 8k;
    proxy_temp_path         data/proxy;
    proxy_intercept_errors  off;

    # include services.conf;
    # include cell_main.conf;

    # fight mhtml/utf-7 bug
    #hat_content             "\r\n";
    #hat_types               text/html text/css;

    # waf, fight hashdos attack
    #waf_max_post_params              1000;
    #waf_max_args                     1000;
    #waf_max_cookies                  1000;
    #waf_post_delimiter_maxlen        70;

    # detector
    #tesla           on;
    #tsl_inject_tail on;
    #tsl_med         on;
    #tsl_med_cookie  _med;
    #tsl_med_jspath  med.js;
    #user_agent_detector on;
    #include detector.conf;

    variables_hash_max_size     1024;
    variables_hash_bucket_size  64;

    server {
        listen              80 default_server;
        server_name         _;

        # if you want to use tmd, you must uncomment tmd main & http & loc conf
        #include tmd_loc.conf;

        # if you want to enable cell logic, you must uncomment following conf,
        # $app: the name of app, eg. 'detail'
        #
        #set                $app                 appname;
        #include            cell_server.conf;

        # detector
        #include detector_srv.conf;

            # if you want to enable cell logic, you must change your proxy_pass conf to following
            #
            # proxy_pass $ups;

             location /console/api {
               proxy_pass http://pre-oneday-workflow-api.alibaba-inc.com;
               proxy_set_header Host pre-oneday-workflow-api.alibaba-inc.com;
               include proxy.conf;
             }

             location /api {
               proxy_pass http://pre-oneday-workflow-api.alibaba-inc.com;
               proxy_set_header Host pre-oneday-workflow-api.alibaba-inc.com;
               include proxy.conf;
             }

             location /v1 {
               proxy_pass http://pre-oneday-workflow-api.alibaba-inc.com;
               proxy_set_header Host pre-oneday-workflow-api.alibaba-inc.com;
               include proxy.conf;
             }

             location /files {
               proxy_pass http://pre-oneday-workflow-api.alibaba-inc.com;
               proxy_set_header Host pre-oneday-workflow-api.alibaba-inc.com;
               include proxy.conf;
             }

             location / {
               proxy_pass http://127.0.0.1:3000;
               proxy_set_header Host $host;
               include proxy.conf;
               add_header 'Access-Control-Allow-Origin' '*';
               add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
               add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
               add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
             }

            location /status.taobao {
                stub_status     on;
            }
    }

    server {
        listen              80;
        server_name         status.taobao.com;

        location            = /nginx_status {
            stub_status     on;
        }
    }

    include apps/*.conf;
}
