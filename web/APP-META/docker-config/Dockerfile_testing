FROM reg.docker.alibaba-inc.com/oneday-workflow/dify-web:0.15.3

# 指定运行时的系统环境变量,如下请替换appName为自己应用名称
ARG APP_NAME
ENV APP_NAME=${APP_NAME}

WORKDIR /home/<USER>/${APP_NAME}/

# 将构建出的主包复制到指定镜像目录中
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz
RUN npm install -g pnpm@10.8.0
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
COPY docker/pnpm-lock.yaml /home/<USER>/${APP_NAME}/pnpm-lock.yaml
# 虚假的tengine，其实是nginx
RUN mkdir -p /opt/taobao/tengine/bin && \
    ln -s /etc/nginx/ /opt/taobao/tengine/conf && \
    ln -s /usr/sbin/nginx /opt/taobao/tengine/bin/tengine && \
    chmod u+x /opt/taobao/tengine/bin/tengine

# 解压到/home/<USER>/来 & 编译新代码
RUN cd /home/<USER>/${APP_NAME}/target/ && \
    tar xvf ${APP_NAME}.tgz && \
    cp -rv /home/<USER>/${APP_NAME}/target/${APP_NAME}/* /home/<USER>/${APP_NAME} && \
    cd /home/<USER>/${APP_NAME}

# 安装&编译&打包
RUN cd /home/<USER>/${APP_NAME} && pnpm install --registry https://registry.npmmirror.com/ && \
    pnpm add -g pm2 && pnpm build
# 复制打包好的脚本出来
RUN cp -rv /home/<USER>/${APP_NAME}/.next/standalone/* /home/<USER>/${APP_NAME}/ && \
    cp -rv  /home/<USER>/${APP_NAME}/.next/static/* /home/<USER>/${APP_NAME}/

# 将应用nginx脚本复制到镜像中
COPY environment/common/nginx/ /home/<USER>/cai/

# 将应用启动脚本和配置文件复制到镜像中
COPY environment/common/bin/ /home/<USER>/${APP_NAME}/bin

# 设置文件操作权限
RUN chmod -R a+x /home/<USER>/${APP_NAME}/bin/ /home/<USER>/cai/bin/ && \
    chown -R admin:root /home/<USER>/ && \
    chmod -R 755 /home/<USER>
    chown -R admin:root /var/lib/nginx/ && \
    chmod -R 755 /var/lib/nginx/

USER admin

RUN echo "/home/<USER>/$APP_NAME/bin/appctl.sh stop" > /home/<USER>/stop.sh && \
echo "/home/<USER>/$APP_NAME/bin/appctl.sh start" >> /home/<USER>/start.sh && \
echo "/home/<USER>/$APP_NAME/bin/preload.sh" > /home/<USER>/health.sh && \
chmod +x /home/<USER>/*.sh


# 挂载数据卷,指定目录挂载到宿主机上面,为了能够保存（持久化）数据以及共享容器间的数据，为了实现数据共享，例如日志文件共享到宿主机或容器间共享数据.
VOLUME /home/<USER>/$APP_NAME/logs \
       /home/<USER>/logs \
       /home/<USER>/diamond \
       /home/<USER>/snapshots \
       /home/<USER>/configclient \
       /home/<USER>/notify \
       /home/<USER>/catserver \
       /home/<USER>/liaoyuan-out \
       /home/<USER>/vipsrv-dns \
       /home/<USER>/vipsrv-failover \
       /home/<USER>/vipsrv-cache \
       /home/<USER>/csp \
       /home/<USER>/.rocketmq_offsets \
       /home/<USER>/amsdata \
       /home/<USER>/amsdata_all

ENTRYPOINT ["/bin/bash", "/home/<USER>/start.sh"]

# Copy entrypoint
COPY docker/entrypoint.sh /home/<USER>/entrypoint.sh
# Copy entrypoint
COPY docker/pm2.json /home/<USER>/${APP_NAME}/pm2.json

# Copy ENV File
COPY .env_testing /home/<USER>/${APP_NAME}/.env


