const translation = {
  steps: {
    header: {
      creation: 'Создать базу знаний',
      update: 'Добавить данные',
      fallbackRoute: 'Знание',
    },
    one: 'Выберите источник данных',
    two: 'Предварительная обработка и очистка текста',
    three: 'Выполнить и завершить',
  },
  error: {
    unavailable: 'Эта база знаний недоступна',
  },
  firecrawl: {
    configFirecrawl: 'Настроить 🔥Firecrawl',
    apiKeyPlaceholder: 'Ключ API с firecrawl.dev',
    getApiKeyLinkText: 'Получите свой ключ API с firecrawl.dev',
  },
  stepOne: {
    filePreview: 'Предварительный просмотр файла',
    pagePreview: 'Предварительный просмотр страницы',
    dataSourceType: {
      file: 'Импортировать из файла',
      notion: 'Синхронизировать из Notion',
      web: 'Синхронизировать с веб-сайта',
    },
    uploader: {
      title: 'Загрузить файл',
      button: 'Перетащите файлы или папки или',
      browse: 'Обзор',
      tip: 'Поддерживаются {{supportTypes}}. Максимум {{size}} МБ каждый.',
      validation: {
        typeError: 'Тип файла не поддерживается',
        size: 'Файл слишком большой. Максимум {{size}} МБ',
        count: 'Несколько файлов не поддерживаются',
        filesNumber: 'Вы достигли лимита пакетной загрузки {{filesNumber}} файлов.',
      },
      cancel: 'Отмена',
      change: 'Изменить',
      failed: 'Ошибка загрузки',
    },
    notionSyncTitle: 'Notion не подключен',
    notionSyncTip: 'Чтобы синхронизировать данные из Notion, сначала необходимо установить соединение с Notion.',
    connect: 'Перейти к подключению',
    button: 'Далее',
    emptyDatasetCreation: 'Я хочу создать пустую базу знаний',
    modal: {
      title: 'Создать пустую базу знаний',
      tip: 'Пустая база знаний не будет содержать документов, и вы можете загружать документы в любое время.',
      input: 'Название базы знаний',
      placeholder: 'Пожалуйста, введите',
      nameNotEmpty: 'Название не может быть пустым',
      nameLengthInvalid: 'Название должно быть от 1 до 40 символов',
      cancelButton: 'Отмена',
      confirmButton: 'Создать',
      failed: 'Ошибка создания',
    },
    website: {
      fireCrawlNotConfigured: 'Firecrawl не настроен',
      fireCrawlNotConfiguredDescription: 'Настройте Firecrawl с API-ключом.',
      configure: 'Настроить',
      run: 'Запустить',
      firecrawlTitle: 'Извлечь веб-контент с помощью 🔥Firecrawl',
      firecrawlDoc: 'Документация Firecrawl',
      firecrawlDocLink: 'https://docs.dify.ai/guides/knowledge-base/sync-from-website',
      options: 'Опции',
      crawlSubPage: 'Сканировать подстраницы',
      limit: 'Лимит',
      maxDepth: 'Максимальная глубина',
      excludePaths: 'Исключить пути',
      includeOnlyPaths: 'Включить только пути',
      extractOnlyMainContent: 'Извлекать только основной контент (без заголовков, навигации, футеров и т. д.)',
      exceptionErrorTitle: 'Произошло исключение при запуске задания Firecrawl:',
      unknownError: 'Неизвестная ошибка',
      totalPageScraped: 'Всего просканировано страниц:',
      selectAll: 'Выбрать все',
      resetAll: 'Сбросить все',
      scrapTimeInfo: 'Всего просканировано {{total}} страниц за {{time}} секунд',
      preview: 'Предварительный просмотр',
      maxDepthTooltip: 'Максимальная глубина сканирования относительно введенного URL. Глубина 0 сканирует только страницу введенного URL, глубина 1 сканирует URL и все, что находится после введенного URL + один /, и так далее.',
      jinaReaderNotConfiguredDescription: 'Настройте Jina Reader, введя свой бесплатный ключ API для доступа.',
      jinaReaderDocLink: 'https://jina.ai/reader',
      useSitemap: 'Использовать карту сайта',
      chooseProvider: 'Выберите провайдера',
      jinaReaderNotConfigured: 'Jina Reader не настроен',
      jinaReaderDoc: 'Узнайте больше о Jina Reader',
      jinaReaderTitle: 'Конвертируйте весь сайт в Markdown',
      useSitemapTooltip: 'Следуйте карте сайта, чтобы просканировать сайт. Если нет, Jina Reader будет сканировать итеративно в зависимости от релевантности страницы, выдавая меньшее количество страниц, но более высокого качества.',
    },
    cancel: 'Отмена',
  },
  stepTwo: {
    segmentation: 'Настройки фрагментации',
    auto: 'Автоматически',
    autoDescription: 'Автоматически устанавливать правила фрагментации и предварительной обработки. Пользователям, не знакомым с системой, рекомендуется выбрать этот вариант.',
    custom: 'Пользовательский',
    customDescription: 'Настроить правила фрагментации, длину фрагментов, правила предварительной обработки и т. д.',
    separator: 'Идентификатор сегмента',
    separatorPlaceholder: 'Например, новая строка (\\\\n) или специальный разделитель (например, "***")',
    maxLength: 'Максимальная длина фрагмента',
    overlap: 'Перекрытие фрагментов',
    overlapTip: 'Установка перекрытия фрагментов может сохранить семантическую связь между ними, улучшая эффект поиска. Рекомендуется установить 10%-25% от максимального размера фрагмента.',
    overlapCheck: 'перекрытие фрагментов не должно превышать максимальную длину фрагмента',
    rules: 'Правила предварительной обработки текста',
    removeExtraSpaces: 'Заменить последовательные пробелы, новые строки и табуляции',
    removeUrlEmails: 'Удалить все URL-адреса и адреса электронной почты',
    removeStopwords: 'Удалить стоп-слова, такие как "a", "an", "the"',
    preview: 'Подтвердить и просмотреть',
    reset: 'Сбросить',
    indexMode: 'Режим индексации',
    qualified: 'Высокое качество',
    recommend: 'Рекомендуется',
    qualifiedTip: 'Вызов интерфейса встраивания системы по умолчанию для обработки, чтобы обеспечить более высокую точность при запросах пользователей.',
    warning: 'Пожалуйста, сначала настройте ключ API поставщика модели.',
    click: 'Перейти к настройкам',
    economical: 'Экономичный',
    economicalTip: 'Используйте автономные векторные движки, индексы ключевых слов и т. д., чтобы снизить точность, не тратя токены',
    QATitle: 'Сегментация в формате вопрос-ответ',
    QATip: 'Включение этой опции приведет к потреблению большего количества токенов',
    QALanguage: 'Сегментировать с помощью',
    estimateCost: 'Оценка',
    estimateSegment: 'Оценочное количество фрагментов',
    segmentCount: 'фрагментов',
    calculating: 'Вычисление...',
    fileSource: 'Предварительная обработка документов',
    notionSource: 'Предварительная обработка страниц',
    websiteSource: 'Предварительная обработка веб-сайта',
    other: 'и другие ',
    fileUnit: ' файлов',
    notionUnit: ' страниц',
    webpageUnit: ' страниц',
    previousStep: 'Предыдущий шаг',
    nextStep: 'Сохранить и обработать',
    save: 'Сохранить и обработать',
    cancel: 'Отмена',
    sideTipTitle: 'Зачем нужна фрагментация и предварительная обработка?',
    sideTipP1: 'При обработке текстовых данных фрагментация и очистка являются двумя важными этапами предварительной обработки.',
    sideTipP2: 'Сегментация разбивает длинный текст на абзацы, чтобы модели могли лучше его понимать. Это улучшает качество и релевантность результатов модели.',
    sideTipP3: 'Очистка удаляет ненужные символы и форматы, делая знания более чистыми и легкими для анализа.',
    sideTipP4: 'Правильная фрагментация и очистка улучшают производительность модели, обеспечивая более точные и ценные результаты.',
    previewTitle: 'Предварительный просмотр',
    previewTitleButton: 'Предварительный просмотр',
    previewButton: 'Переключение в формат вопрос-ответ',
    previewSwitchTipStart: 'Текущий предварительный просмотр фрагмента находится в текстовом формате, переключение на предварительный просмотр в формате вопрос-ответ',
    previewSwitchTipEnd: ' потребляет дополнительные токены',
    characters: 'символов',
    indexSettingTip: 'Чтобы изменить метод индексации, пожалуйста, перейдите в ',
    retrievalSettingTip: 'Чтобы изменить метод индексации, пожалуйста, перейдите в ',
    datasetSettingLink: 'настройки базы знаний.',
    separatorTip: 'Разделитель — это символ, используемый для разделения текста. \\n\\n и \\n — это часто используемые разделители для разделения абзацев и строк. В сочетании с запятыми (\\n\\n,\\n) абзацы будут сегментированы по строкам, если максимальная длина блока превышает их. Вы также можете использовать специальные разделители, определенные вами (например, ***).',
    maxLengthCheck: 'Максимальная длина блока должна быть меньше {{limit}}',
    switch: 'Выключатель',
    parentChunkForContext: 'Родительский блок для контекста',
    previewChunkTip: 'Нажмите кнопку «Предварительный просмотр фрагмента» слева, чтобы загрузить предварительный просмотр',
    notAvailableForParentChild: 'Недоступно для индекса типа "родитель-потомок"',
    parentChildChunkDelimiterTip: 'Разделитель — это символ, используемый для разделения текста. \\n рекомендуется для разбиения родительских блоков на небольшие дочерние блоки. Вы также можете использовать специальные разделители, определенные самостоятельно.',
    previewChunk: 'Предварительный просмотр фрагмента',
    previewChunkCount: '{{Количество}} Предполагаемые куски',
    generalTip: 'Общий режим фрагментации текста, извлекаемые и вызываемые фрагменты одинаковы.',
    general: 'Общее',
    useQALanguage: 'Фрагмент с использованием формата Q&A в',
    notAvailableForQA: 'Недоступно для индекса Q&A',
    paragraph: 'Параграф',
    parentChild: 'Родитель-дочерний',
    fullDoc: 'Полный документальный фильм',
    qaSwitchHighQualityTipTitle: 'Формат вопросов и ответов требует высококачественного метода индексации',
    parentChildDelimiterTip: 'Разделитель — это символ, используемый для разделения текста. \\n\\n рекомендуется для разделения исходного документа на большие родительские части. Вы также можете использовать специальные разделители, определенные самостоятельно.',
    parentChildTip: 'При использовании режима «родитель-потомок» дочерний блок используется для извлечения, а родительский блок — для вызова в качестве контекста.',
    paragraphTip: 'В этом режиме текст разбивается на абзацы на основе разделителей и максимальной длины блока, используя разделенный текст в качестве родительского блока для извлечения.',
    highQualityTip: 'После завершения встраивания в режиме «Высокое качество» возврат к экономичному режиму невозможен.',
    childChunkForRetrieval: 'Детский фрагмент для извлечения',
    qaSwitchHighQualityTipContent: 'В настоящее время только высококачественный метод индекса поддерживает фрагментацию формата Q&A. Хотели бы вы перейти в режим высокого качества?',
    fullDocTip: 'Весь документ используется в качестве родительского блока и извлекается напрямую. Обратите внимание, что по причинам производительности текст, превышающий 10000 токенов, будет автоматически обрезан.',
  },
  stepThree: {
    creationTitle: '🎉 База знаний создана',
    creationContent: 'Мы автоматически назвали базу знаний, вы можете изменить ее в любое время',
    label: 'Название базы знаний',
    additionTitle: '🎉 Документ загружен',
    additionP1: 'Документ был загружен в базу знаний',
    additionP2: ', вы можете найти его в списке документов базы знаний.',
    stop: 'Остановить обработку',
    resume: 'Возобновить обработку',
    navTo: 'Перейти к документу',
    sideTipTitle: 'Что дальше',
    sideTipContent: 'После завершения индексации документа база знаний может быть интегрирована в приложение в качестве контекста, вы можете найти настройку контекста на странице prompt orchestration. Вы также можете создать-workflow приложение как отдельный как независимый плагин.',
    modelTitle: 'Вы уверены, что хотите остановить встраивание?',
    modelContent: 'Если вам нужно будет возобновить обработку позже, вы продолжите с того места, где остановились.',
    modelButtonConfirm: 'Подтвердить',
    modelButtonCancel: 'Отмена',
  },
  jinaReader: {
    getApiKeyLinkText: 'Получите бесплатный ключ API в jina.ai',
    configJinaReader: 'Настройка Jina Reader',
    apiKeyPlaceholder: 'Ключ API от jina.ai',
  },
  otherDataSource: {
    learnMore: 'Подробнее',
    title: 'Подключаться к другим источникам данных?',
    description: 'В настоящее время база знаний Dify имеет лишь ограниченные источники данных. Добавление источника данных в базу знаний Dify — это отличный способ повысить гибкость и возможности платформы для всех пользователей. Наше руководство по вкладу поможет вам легко начать работу. Пожалуйста, нажмите на ссылку ниже, чтобы узнать больше.',
  },
}

export default translation
